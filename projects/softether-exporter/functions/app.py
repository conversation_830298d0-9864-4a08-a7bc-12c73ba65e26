import os
import time
from datetime import datetime, timezone, timedelta

from aws_lambda_powertools.utilities.typing import LambdaContext
from aws_lambda_powertools import Logger

from ..softether import SoftEther

logger = Logger()

OFFLINE_THRESHOLD_MINUTES = int(os.environ.get("OFFLINE_THRESHOLD_MINUTES", 10))
IGNORE_HUBS = os.environ.get("IGNORE_HUBS", "").split(",")
MAX_RETRIES = int(os.environ.get("MAX_RETRIES", 3))
RETRY_DELAY = int(os.environ.get("RETRY_DELAY", 2))  # seconds

try:
    VPN_HOST: str = os.environ["VPN_HOST"]
    VPN_PORT: int = int(os.environ["VPN_PORT"])
    VPN_PASS: str = os.environ["VPN_PASS"]
except (<PERSON><PERSON><PERSON><PERSON>, ValueError) as exc:
    raise RuntimeError("VPN_* environment variables are not set correctly") from exc


@logger.inject_lambda_context(log_event=True)
def handler(event: dict, context: LambdaContext):
    """
    Lambda function to monitor SoftEther VPN server status and update the database.
    This function connects to the SoftEther VPN server, retrieves the list of users
    and their connection status, and updates the database accordingly.
    """
    client = SoftEther(host=VPN_HOST, port=VPN_PORT, password=VPN_PASS, timeout=15)

    try:
        hubs = client.enum_hub()
        logger.debug(f"Found {len(hubs)} hubs to monitor")
    except Exception as e:
        logger.error(f"Failed to enumerate hubs: {e}")
        return {"statusCode": 500, "error": "Failed to enumerate hubs"}

    for hub in hubs:
        hubname = hub["HubName_str"]
        if hubname in IGNORE_HUBS:
            logger.debug(f"Skipping ignored hub: {hubname}")
            continue  # explicit opt‑out

        logger.debug(f"Processing hub: {hubname}")

        # Process hub with retry logic
        success = _process_hub_with_retry(client, hubname)
        if not success:
            logger.error(f"Failed to process hub {hubname} after {MAX_RETRIES} retries")

    return {"statusCode": 200}


def _process_hub_with_retry(client: SoftEther, hubname: str) -> bool:
    """
    Process a hub with retry logic to handle transient failures.
    Returns True if successful, False if all retries failed.
    """
    for attempt in range(MAX_RETRIES):
        try:
            # Get users and sessions for this hub
            all_users = client.enum_user(hubname)
            online_sessions = client.enum_session(hubname)

            # Set of usernames currently online
            online_usernames = {session["Username_str"] for session in online_sessions}

            logger.info(f"Hub {hubname}: {len(all_users)} users, {len(online_sessions)} online sessions")

            # For each user in the hub
            for user in all_users:
                username = user["Name_str"]

                if any(keyword in username.lower() for keyword in ("icinga", "dhcp")):
                    continue  # Skip icinga and dhcp servers

                is_connected = username in online_usernames
                _update_server_status_with_threshold(hubname, username, is_connected)

            return True  # Success

        except Exception as e:
            logger.warning(f"Attempt {attempt + 1}/{MAX_RETRIES} failed for hub {hubname}: {e}")
            if attempt < MAX_RETRIES - 1:
                time.sleep(RETRY_DELAY)
            else:
                logger.error(f"All {MAX_RETRIES} attempts failed for hub {hubname}")
                return False

    return False


def _update_server_status_with_threshold(
    customer: str, server_name: str, is_connected: bool
) -> None:
    """Upsert *ServerStatus* with debouncing & TTL refresh."""

    now = datetime.now(timezone.utc)
    ts = int(now.timestamp())
    threshold_ts = int((now - timedelta(minutes=OFFLINE_THRESHOLD_MINUTES)).timestamp())
    ttl_timestamp = int((now + timedelta(days=100)).timestamp())

    if is_connected:
        # ONLINE ----------------------------------------------------------------
        try:
            # Heartbeat + TTL refresh
            ServerStatus(customer, server_name).update(
                actions=[
                    ServerStatus.UpdatedAt.set(ts),
                    ServerStatus.IsOnline.set(True),
                    ServerStatus.TTL.set(ttl_timestamp),
                ]
            )
        except UpdateError:
            pass

        # Ensure AlertsEnabled exists (default → True)
        try:
            ServerStatus(customer, server_name).update(
                actions=[ServerStatus.AlertsEnabled.set(True)],
                condition=ServerStatus.AlertsEnabled.does_not_exist(),
            )
        except UpdateError:
            pass  # Condition not met

    else:
        # OFFLINE ---------------------------------------------------------------
        try:
            ServerStatus(customer, server_name).update(
                actions=[
                    ServerStatus.UpdatedAt.set(ts),
                    ServerStatus.IsOnline.set(False),
                    ServerStatus.TTL.set(ttl_timestamp),
                ],
                condition=(
                    (ServerStatus.IsOnline == True)
                    & (
                        ServerStatus.UpdatedAt.does_not_exist()
                        | (ServerStatus.UpdatedAt < threshold_ts)
                    )
                ),
            )
        except UpdateError:
            pass
