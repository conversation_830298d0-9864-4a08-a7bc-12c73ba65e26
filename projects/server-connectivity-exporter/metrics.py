from collections.abc import Callable

import prometheus_client as prom
from aws_lambda_powertools import Logger
from prometheus_client.exposition import basic_auth_handler

from .config import Settings

logger = Logger()


class ServerStatusMetric:
    """A Prometheus metric for server status."""

    def __init__(self, settings: Settings) -> None:
        self.settings = settings
        self.registry = prom.CollectorRegistry()
        self.server_alive = prom.Gauge(
            "alive_status",
            "Indicates if the server is alive (1) or not (0)",
            labelnames=["site_id", "environment"],
            registry=self.registry,
            namespace="eyecue_server",
        )

    def update(self, server_name: str, is_online: bool) -> None:
        metric_value = 1 if is_online else 0
        self.server_alive.labels(server_name, self.settings.environment).set(metric_value)
        prom.push_to_gateway(
            self.settings.metrics.victoria_metrics_url,
            job="server-connectivity-alerts",
            registry=self.registry,
            handler=self._auth_handler,
        )

    def _auth_handler(
        self,
        url: str,
        method: str,
        timeout: float | None,
        headers: list[tuple[str, str]],
        data: bytes,
    ) -> Callable[[], None]:
        """Create an authentication handler for Victoria Metrics push gateway.

        https://prometheus.github.io/client_python/exporting/pushgateway/#handlers-for-authentication
        """
        return basic_auth_handler(
            url,
            method,
            timeout,
            headers,
            data,
            self.settings.metrics.victoria_metrics_username,
            self.settings.metrics.victoria_metrics_password,
        )
