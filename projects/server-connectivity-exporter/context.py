from dataclasses import dataclass, field

from .config import Settings
from .softether import SoftEther
from .metrics import ServerStatusMetric

@dataclass
class Context:
    settings: Settings = field(default_factory=Settings)
    softether: SoftEther = field(init=False)
    server_status_metric: ServerStatusMetric = field(init=False)

    def __post_init__(self) -> None:
        self.softether = SoftEther(
            host=self.settings.softether.host,
            port=self.settings.softether.port,
            password=self.settings.softether.password,
            timeout=15,
        )
        self.server_status_metric = ServerStatusMetric(self.settings)
