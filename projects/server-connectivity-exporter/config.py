from pydantic import Partial
from pydantic_settings import BaseSettings, SettingsConfigDict

class MetricsSettings(BaseSettings):
    victoria_metrics_url: str
    victoria_metrics_username: str
    victoria_metrics_password: str

    model_config = SettingsConfigDict(env_file=".env", extra="ignore")


class SoftEtherSettings(BaseSettings):
    host: str
    port: int
    password: str

    model_config = SettingsConfigDict(env_file=".env", extra="ignore")


class Settings(BaseSettings):
    ignore_hubs: list[str] = []
    environment: Partial["dev", "stg", "prod"]
    softether: SoftEtherSettings = SoftEtherSettings()
    metrics: MetricsSettings = MetricsSettings()

    model_config = SettingsConfigDict(env_file=".env", extra="ignore")


settings = Settings()
